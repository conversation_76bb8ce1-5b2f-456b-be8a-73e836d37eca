package org.springframework.ai.mcp.sample.client;

import com.alibaba.fastjson.JSONObject;
import com.esign.ai.mcp.sample.server.client.GatewayClient;
import com.esign.ai.mcp.sample.server.tools.FileToolService;
import com.esign.ai.mcp.sample.server.tools.model.Response;
import com.esign.ai.mcp.sample.server.util.HttpClientUtil;
import com.esign.ai.mcp.sample.server.util.MessageDigestSignUtil;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.ResponseEntity;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

public class ClientSseTest {

//    @Test
    public void testMain1() throws Exception {
        String filePath = "/Users/<USER>/Downloads/电子印章授权书.pdf";
        Path path = Paths.get(filePath);
        byte[] bytes = Files.readAllBytes(path);
//        String contentMd51 = new FileToolService().getFileContentMD5(filePath);
//        System.out.println("文件md5加密内容："+contentMd51);
        String contentMd5 = MessageDigestSignUtil.hashAndBase64("MD5", bytes);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("contentMd5", contentMd5);
        jsonObject.put("contentType", "application/octet-stream");
        jsonObject.put("fileName", path.getFileName().toString());
        jsonObject.put("fileSize", Files.size(path));
        jsonObject.put("convertToPDF", true);
        ResponseEntity<Response<FileToolService.GetUploadUrl>> responseEntity = new GatewayClient().doRequest("/v3/files/file-upload-url", "POST", jsonObject.toJSONString(), new ParameterizedTypeReference<Response<FileToolService.GetUploadUrl>>(){ });

        var response = responseEntity.getBody();

        System.out.println("文件id："+response.getData().fileId());
        System.out.println(response.getData().fileUploadUrl());

        String url = response.getData().fileUploadUrl();

        Map<String, String> map = new HashMap<>();
        map.put("Content-MD5", contentMd5);
        map.put("Content-Type", "application/octet-stream");
        String request = HttpClientUtil.sendPutRequest(url, map, bytes);

        System.out.println(request);


    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme