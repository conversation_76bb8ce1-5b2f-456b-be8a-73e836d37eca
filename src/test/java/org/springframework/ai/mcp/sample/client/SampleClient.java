/*
* Copyright 2024 - 2024 the original author or authors.
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
* https://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/
package org.springframework.ai.mcp.sample.client;

import java.time.Duration;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.spec.McpClientTransport;
import io.modelcontextprotocol.spec.McpSchema.CallToolRequest;
import io.modelcontextprotocol.spec.McpSchema.CallToolResult;
import io.modelcontextprotocol.spec.McpSchema.ListToolsResult;

/**
 * <AUTHOR>
 */

public class SampleClient {

	private final McpClientTransport transport;

	public SampleClient(McpClientTransport transport) {
		this.transport = transport;
	}

	public void run() {

		var client = McpClient.sync(this.transport)
               .requestTimeout(Duration.ofSeconds(10)) // 增加超时时间到 10 秒,默认20秒
               .build();

		client.initialize();

		client.ping();

		// List and demonstrate tools
		ListToolsResult toolsList = client.listTools();
		System.out.println("Available Tools = " + toolsList);

		CallToolResult callToolResult = client.callTool(new CallToolRequest("toUpperCase",
				Map.of("input", "example test")));
		System.out.println("toUpperCase Response =  " + callToolResult);

		client.closeGracefully();

	}

	public void callTools(List<CallToolRequest> callToolRequestList) {

		var client = McpClient.sync(this.transport)
				.requestTimeout(Duration.ofSeconds(30)) // 增加超时时间到 10 秒
				.build();
		try(client) {
			client.initialize();

			client.ping();

			// List and demonstrate tools
			ListToolsResult toolsList = client.listTools();
			System.out.println("Available Tools = " + JSONObject.toJSONString(toolsList));

			callToolRequestList.forEach(request -> {
				var callToolResult = client.callTool(request);
				System.out.println("tool = " + request.name() + " Response =  " + callToolResult);
			});

		}finally {
			client.closeGracefully();
		}


	}

}
