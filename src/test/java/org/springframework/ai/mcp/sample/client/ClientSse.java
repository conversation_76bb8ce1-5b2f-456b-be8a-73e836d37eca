package org.springframework.ai.mcp.sample.client;

import io.modelcontextprotocol.client.transport.WebFluxSseClientTransport;

import io.modelcontextprotocol.spec.McpSchema;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ClientSse {

	public static void main(String[] args) {
		var transport = new WebFluxSseClientTransport(WebClient.builder().baseUrl("http://localhost:8080"));
		var client = new SampleClient(transport);

		String param = "{\"sealRole\":\"SEAL_USER\",\"authorizedPsnIds\":[\"ALL\"],\"sealAuthScope\":{\"applicationsIds\":[\"7876718920\"]},\"orgId\":\"7ca3f01bf52c4e48a157c22e6b6c635e\",\"sealId\":\"263b5472-f9ba-4b3c-b572-36511c2bff69\",\"transactorPsnId\":\"6c4406ccd6d04c9c82650ee398033930\",\"effectiveTime\":1744041600000,\"expireTime\":1744127999000,\"longTermEffective\":true,\"redirectUrl\":null,\"appScheme\":null,\"customBizNum\":null,\"autoSign\":false,\"authConfirmMethod\":1}";
		String signParam = "{\"signTemplateId\":\"67245f2c26b54f94a40f2f2bf69f75e7\",\"signFlowInitiator\":{\"orgId\":\"4af6af757f7b45ab99d7f014c1f2f98b\",\"transactor\":{\"psnId\":\"3e60c0b6f3fc4e1082e24b6836a589af\"}},\"signFlowConfig\":{\"signFlowTitle\":\"桃浪流程模板发起_123\",\"autoFinish\":true,\"autoStart\":true,\"noticeConfig\":{\"noticeTypes\":\"1\"},\"notifyUrl\":\"http://***********:8080/testnotify/msgRecive\",\"signConfig\":{\"availableSignClientTypes\":\"1\",\"autoFillAndSubmit\":false,\"editComponentValue\":false},\"authConfig\":{\"psnAvailableAuthModes\":[\"PSN_MOBILE3\",\"PSN_FACE\",\"PSN_BANKCARD4\"],\"orgAvailableAuthModes\":[\"ORG_BANK_TRANSFER\",\"ORG_LEGALREP\",\"ORG_LEGALREP_AUTHORIZATION\",\"ORG_ALIPAY_CREDIT\"]}},\"participants\":[{\"participantId\":\"7874e22aa67d46029f9b758bdb2f2e8b\",\"orgParticipant\":{\"orgName\":\"esigntest库力库力企业\",\"transactor\":{\"transactorPsnAccount\":\"***********\",\"transactorName\":\"测试猫猫八\"}}}]}";

		Map<String, Object> map = new HashMap<>();
		map.put("signTemplateId", "b836ddfc9af942d091105c11cbf3d855");
		map.put("signFlowTitle", "三一模版发起测试-mcp1");
		map.put("psnParticipant", Map.of("psnAccount","***********","psnName","潘城杰"));
		map.put("orgParticipant", Map.of("orgName","esigntest阿斯顿","transactor",Map.of("transactorPsnAccount","***********","transactorName","潘城杰")));


		List<McpSchema.CallToolRequest> callToolRequests = List.of(
//				new McpSchema.CallToolRequest("查询企业内部印章", Map.of("orgId", "7ca3f01bf52c4e48a157c22e6b6c635e", "pageNum", 1, "pageSize", 2)),
//				new McpSchema.CallToolRequest("内部成员印章授权", Map.of("json", param)),

//				new McpSchema.CallToolRequest("查询签署流程详情", Map.of("signFlowId", "e0d54a2553f945b6a19321ec653ec61e")),
//				new McpSchema.CallToolRequest("查询流程模板列表", Map.of("orgId", "7ca3f01bf52c4e48a157c22e6b6c635e")),

				new McpSchema.CallToolRequest("通过流程模板创建合同拟定和签署流程", map)
		);
		client.callTools(callToolRequests);

	}

}
