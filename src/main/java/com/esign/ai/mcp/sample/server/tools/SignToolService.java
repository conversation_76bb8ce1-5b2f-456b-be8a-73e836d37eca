package com.esign.ai.mcp.sample.server.tools;

import com.alibaba.fastjson.JSONObject;
import com.esign.ai.mcp.sample.server.client.GatewayClient;
import com.esign.ai.mcp.sample.server.tools.model.Response;
import com.esign.ai.mcp.sample.server.tools.model.input.CreateSignFlowByFile;
import com.esign.ai.mcp.sample.server.tools.model.output.SignFLowIdOutput;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName SignToolService
 * @Description
 * <AUTHOR>
 * @Date 2025/4/15 15:15
 **/
@Service
public class SignToolService implements IToolService{
    private static final Logger log = LoggerFactory.getLogger(SignToolService.class);

    @Resource
    GatewayClient gatewayClient;

    @Tool(name = "基于文件发起签署", description = "开发者通过已上传的文件id，发起签署流程。个人参与方或者企业参与方至少填写一个。")
    public Response<SignFLowIdOutput> createSignFlow(@ToolParam(description = "文件id") String fileId,
                                     @ToolParam(description = "签署任务主题") String signFlowTitle,
                                     @ToolParam(description = "企业参与方企业名称", required = false)String orgName,
                                     @ToolParam(description = "企业参与方经办人手机号", required = false)String transactorPsnAccount,
                                     @ToolParam(description = "企业参与方经办人姓名", required = false)String transactorName,
                                     @ToolParam(description = "个人参与方手机号", required = false)String psnAccount,
                                     @ToolParam(description = "个人参与方姓名", required = false)String psnName) {

        PsnParticipant psnParticipant = StringUtils.isAnyBlank(psnAccount) ? null : new PsnParticipant(psnAccount, psnName);
        OrgParticipant orgParticipant  = StringUtils.isAnyBlank(orgName, transactorPsnAccount) ? null:new OrgParticipant(orgName, transactorPsnAccount, transactorName);
        CreateSignFlowByFile createSignFlowByFile = convert(fileId, signFlowTitle, psnParticipant, orgParticipant);
        ResponseEntity<Response<SignFLowIdOutput>> responseEntity = gatewayClient.doRequest("/v3/sign-flow/create-by-file", "POST", JSONObject.toJSONString(createSignFlowByFile), new ParameterizedTypeReference<Response<SignFLowIdOutput>>() {});

        return responseEntity.getBody();
    }

    private CreateSignFlowByFile convert(String fileId,
                                         String signFlowTitle,
                                         PsnParticipant psnParticipant,
                                         OrgParticipant orgParticipant){
        CreateSignFlowByFile createSignFlowByFile = CreateSignFlowByFile.builder().build();
        createSignFlowByFile.setDocs(List.of(CreateSignFlowByFile.Doc.builder().fileId(fileId).build()));
        createSignFlowByFile.setSignFlowConfig(CreateSignFlowByFile.SignFlowConfig.builder()
                .signFlowTitle(signFlowTitle)
                .autoFinish(true)
                .noticeConfig(CreateSignFlowByFile.SignFlowConfig.NoticeConfig.builder()
                       .noticeTypes("1")
                       .build())
                .build());
        createSignFlowByFile.setSigners(new ArrayList<>());
        //个人签署方信息
        if(psnParticipant != null){
            createSignFlowByFile.getSigners().add(
                CreateSignFlowByFile.Signer.builder()
                        .signerType(0)
                        .psnSignerInfo(CreateSignFlowByFile.Signer.PsnSignerInfo.builder()
                              .psnAccount(psnParticipant.psnAccount)
                              .psnInfo(CreateSignFlowByFile.Signer.PsnSignerInfo.PsnInfo.builder()
                                      .psnName(psnParticipant.psnName)
                                      .build()
                              ).build())
                        .signFields(List.of(CreateSignFlowByFile.Signer.SignField.builder()
                                .fileId(fileId)
                                .normalSignFieldConfig(
                                        CreateSignFlowByFile.Signer.SignField.NormalSignFieldConfig.builder()
                                                .signFieldStyle(1)
                                                .freeMode(true)
                                                .build())
                                .build()))
                        .build()
            );
        }
        //企业签署方信息
        if(orgParticipant!= null){
            createSignFlowByFile.getSigners().add(
                CreateSignFlowByFile.Signer.builder()
                        .signerType(1)
                        .orgSignerInfo(CreateSignFlowByFile.Signer.OrgSignerInfo.builder()
                                .orgName(orgParticipant.orgName)
                                .transactorInfo(CreateSignFlowByFile.Signer.PsnSignerInfo.builder()
                                        .psnAccount(orgParticipant.transactorPsnAccount)
                                        .psnInfo(CreateSignFlowByFile.Signer.PsnSignerInfo.PsnInfo.builder()
                                                .psnName(orgParticipant.transactorName)
                                                .build())
                                        .build())
                                .build())
                        .signFields(List.of(CreateSignFlowByFile.Signer.SignField.builder()
                                .fileId(fileId)
                                .normalSignFieldConfig(
                                        CreateSignFlowByFile.Signer.SignField.NormalSignFieldConfig.builder()
                                                .signFieldStyle(1)
                                                .freeMode(true)
                                                .build())
                                .build()))
                        .build()
            );
        }
        return createSignFlowByFile;
    }

    @JsonIgnoreProperties
    public record OrgParticipant(@ToolParam(description = "企业参与方企业名称 orgName", required = false)String orgName,
                                 @ToolParam(description = "企业参与方经办人手机号 transactorPsnAccount", required = false)String transactorPsnAccount,
                                 @ToolParam(description = "企业参与方经办人姓名 transactorName", required = false)String transactorName){
    }
    @JsonIgnoreProperties
    public record PsnParticipant(@ToolParam(description = "个人参与方手机号 psnAccount", required = false)String psnAccount,
                                 @ToolParam(description = "个人参与方姓名 psnName", required = false)String psnName){

    }



    @Tool(name = "查询签署流程详情", description = "查询签署流程的基本信息")
    public JSONObject queryBySignFlowId(@ToolParam(description = "签署流程ID") String signFlowId) {
        Object response = gatewayClient.doHttpRequest("/v3/sign-flow/"+signFlowId+"/detail", "GET", "").body(Object.class);
        return JSONObject.parseObject(JSONObject.toJSONString(response));
    }
}
