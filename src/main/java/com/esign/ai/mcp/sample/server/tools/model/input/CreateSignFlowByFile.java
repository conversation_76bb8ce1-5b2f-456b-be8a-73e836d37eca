package com.esign.ai.mcp.sample.server.tools.model.input;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class CreateSignFlowByFile {
    private List<Doc> docs ;

    private SignFlowConfig signFlowConfig;

    private List<Signer> signers ;

    @Data
    @Builder
    public static class Doc {
        private String fileId;

        private String fileName;
    }

    @Data
    @Builder
    public static class SignFlowConfig {
        private String signFlowTitle;

        private boolean autoFinish;

        private NoticeConfig noticeConfig;

        @Data
        @Builder
        public static class NoticeConfig {
            private String noticeTypes;
        }
    }

    @Data
    @Builder
    public static class Signer {
        //签署方类型 0 - 个人，1 -企业/机构
        private int signerType;
        //企业签署方信息
        private OrgSignerInfo orgSignerInfo;
        //个人签署方信息
        private PsnSignerInfo psnSignerInfo;
        //签署区信息
        private List<SignField> signFields ;

        @Data
        @Builder
        public static class OrgSignerInfo {
            //企业名称
            private String orgName;
            //企业经办人
            private PsnSignerInfo transactorInfo;
        }

        @Data
        @Builder
        public static class PsnSignerInfo {
            //个人手机号或邮箱
            private String psnAccount;
            private PsnInfo psnInfo;

            @Data
            @Builder
            public static class PsnInfo {
                //个人姓名
                private String psnName;
            }
        }

        @Data
        @Builder
        public static class SignField {
            //签署区所在待签署文件ID
            private String fileId;
            //签章区配置项
            private NormalSignFieldConfig normalSignFieldConfig;

            @Data
            @Builder
            public static class NormalSignFieldConfig {
                //签章区样式 1 - 单页签章，2 - 骑缝签章
                private int signFieldStyle;
                //是否自由签章，默认值 false
                private boolean freeMode;
            }
        }
    }
}

