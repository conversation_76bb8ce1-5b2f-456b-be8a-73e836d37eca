package com.esign.ai.mcp.sample.server.util;

import com.alibaba.fastjson.JSONArray;
import org.apache.http.HttpEntity;
import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.Map;

// 定义一个支持长连接且可传请求头的 HTTP 客户端工具类
public class HttpClientUtil {
    // 连接池管理器，用于管理长连接
    private static PoolingHttpClientConnectionManager connectionManager;

    // 初始化连接池管理器
    private static synchronized void initConnectionManager() {
        if (connectionManager == null) {
            connectionManager = new PoolingHttpClientConnectionManager();
            // 设置最大连接数
            connectionManager.setMaxTotal(200);
            // 设置每个路由的最大连接数
            connectionManager.setDefaultMaxPerRoute(20);
        }
    }

    // 获取一个支持长连接的 HttpClient 实例
    private static CloseableHttpClient getHttpClient() {
        if (connectionManager == null) {
            initConnectionManager();
        }
        return HttpClients.custom()
                .setConnectionManager(connectionManager)
                .build();
    }

    // 发送 GET 请求的方法，支持传递请求头
    public static byte[] sendGetRequest(String url, Map<String, String> headers) throws IOException {
        // 获取支持长连接的 HttpClient 实例
        CloseableHttpClient httpClient = getHttpClient();
//        try () {
            // 创建一个 HttpGet 请求对象，传入请求的 URL
            HttpGet httpGet = new HttpGet(url);
            // 添加请求头
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpGet.addHeader(entry.getKey(), entry.getValue());
                }
            }
            // 执行 GET 请求并获取响应
            HttpResponse response = httpClient.execute(httpGet);
            // 获取响应的实体
            HttpEntity entity = response.getEntity();
            // 如果实体不为空，将实体内容转换为字符串并返回
            if (entity != null) {
               return EntityUtils.toByteArray(entity);
            }
//        }
        return null;
    }

    public static String sendPutRequest(String url, Map<String, String> headers, byte[] bytes) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPut httpPut = new HttpPut(url);
            // 添加请求头
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpPut.addHeader(entry.getKey(), entry.getValue());
                }
            }
            byte[] paramBytes = (byte[])bytes;
            ((HttpEntityEnclosingRequest) httpPut).setEntity(new ByteArrayEntity(paramBytes));
            // 执行 POST 请求并获取响应
            HttpResponse response = httpClient.execute(httpPut);
            if(response.getStatusLine().getStatusCode()!=200){
                System.err.println("请求失败，状态码：" + response.getStatusLine().getStatusCode());
                System.err.println("请求失败，header：" + JSONArray.toJSONString(response.getAllHeaders()));
            }

            // 获取响应的实体
            HttpEntity responseEntity = response.getEntity();
            // 如果实体不为空，将实体内容转换为字符串并返回
            if (responseEntity != null) {
                String putResponse = EntityUtils.toString(responseEntity,"UTF-8");
                System.out.println("PUT 请求响应: " + putResponse);
                return putResponse;
            }
        } catch (ClientProtocolException e) {
            System.err.println("HTTP 协议异常: " + e.getMessage());
        } catch (IOException e) {
            System.err.println("IO 异常: " + e.getMessage());
        }
        return null;
    }

    // 发送 POST 请求的方法，支持传递请求头和使用 JSON 请求体
    public static String sendPostRequest(String url, Map<String, String> headers, String jsonBody) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 创建一个 HttpPost 请求对象，传入请求的 URL
            HttpPost httpPost = new HttpPost(url);

            // 添加请求头
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpPost.addHeader(entry.getKey(), entry.getValue());
                }
            }

            // 设置请求体
            if (jsonBody != null && !jsonBody.isEmpty()) {
                StringEntity entity = new StringEntity(jsonBody, "UTF-8");
                httpPost.setEntity(entity);
                // 如果没有手动设置 Content-Type，默认设置为 application/json
                if (!headers.containsKey("Content-Type")) {
                    httpPost.addHeader("Content-Type", "application/json");
                }
            }

            // 执行 POST 请求并获取响应
            HttpResponse response = httpClient.execute(httpPost);

            if(response.getStatusLine().getStatusCode()!=200){
                System.err.println("请求失败，状态码：" + response.getStatusLine().getStatusCode());
                System.err.println("请求失败，header：" + JSONArray.toJSONString(response.getAllHeaders()));
            }

            // 获取响应的实体
            HttpEntity responseEntity = response.getEntity();
            // 如果实体不为空，将实体内容转换为字符串并返回
            if (responseEntity != null) {
                String postResponse = EntityUtils.toString(responseEntity,"UTF-8");
                System.out.println("POST 请求响应: " + postResponse);
                return postResponse;
            }
        } catch (ClientProtocolException e) {
            System.err.println("HTTP 协议异常: " + e.getMessage());
        } catch (IOException e) {
            System.err.println("IO 异常: " + e.getMessage());
        }
        return null;
    }
}
