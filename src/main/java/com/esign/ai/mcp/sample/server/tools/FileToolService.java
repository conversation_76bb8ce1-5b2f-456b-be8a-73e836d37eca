package com.esign.ai.mcp.sample.server.tools;

import com.alibaba.fastjson.JSONObject;
import com.esign.ai.mcp.sample.server.client.GatewayClient;
import com.esign.ai.mcp.sample.server.tools.model.Response;
import com.esign.ai.mcp.sample.server.util.HttpClientUtil;
import com.esign.ai.mcp.sample.server.util.MessageDigestSignUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName FileToolService
 * @Description
 * <AUTHOR>
 * @Date 2025/4/10 17:25
 **/
@Service
public class FileToolService implements IToolService{

    private static final Logger log = LoggerFactory.getLogger(FileToolService.class);

    @Resource
    GatewayClient gatewayClient;

    @Tool(name = "上传本地文件", description = "将本地文件上传至e签宝服务端，可用于制作模板、发起合同签署等。")
    public Response<Map<String, String>> getUploadUrl(@ToolParam(description = "文件名", required = false) String fileName,
                                                      @ToolParam(description = "文件链接") String fileUrl) {

        try {
            String name;
            long size;
            byte[] bytes;

            if(fileUrl.startsWith("http")){
                name= fileName;
                bytes = HttpClientUtil.sendGetRequest(fileUrl, null);
                size = bytes.length;
            }else {
                Path path = Paths.get(fileUrl);
                bytes = Files.readAllBytes(path);
                name = path.getFileName().toString();
                size = Files.size(path);
            }

            //获取文件上传地址
            String contentMd5 = MessageDigestSignUtil.hashAndBase64("MD5", bytes);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("contentMd5", contentMd5);
            jsonObject.put("contentType", "application/octet-stream");
            jsonObject.put("fileName", name);
            jsonObject.put("fileSize", size);
            jsonObject.put("convertToPDF", true);
            ResponseEntity<Response<GetUploadUrl>> responseEntity = gatewayClient.doRequest("/v3/files/file-upload-url", "POST", jsonObject.toJSONString(), new ParameterizedTypeReference<Response<GetUploadUrl>>(){ });

            var response = responseEntity.getBody();
            //上传文件
            Map<String, String> heaher = new HashMap<>();
            heaher.put("Content-MD5", contentMd5);
            heaher.put("Content-Type", "application/octet-stream");
            String putResponse = HttpClientUtil.sendPutRequest(response.getData().fileUploadUrl, heaher, bytes);

            Map<String, String> result = new HashMap<>();
            result.put("fileId", response.getData().fileId);
            return new Response<>(0, putResponse, result);

        } catch (IOException e) {
            log.warn("读取文件异常 name={}, fileUrl={}", fileName, fileUrl, e);
            throw new RuntimeException("读取文件异常");
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record GetUploadUrl(@JsonProperty("fileId") String fileId, @JsonProperty("fileUploadUrl") String fileUploadUrl) { }

    @Tool(name = "查询文件上传状态", description = "查询文件在e签宝服务端的上传状态。")
    public Response<QueryFileStatus> queryFileStatus(@ToolParam(description = "文件ID") String fileId) {

        ResponseEntity<Response<QueryFileStatus>> responseEntity = gatewayClient.doRequest("/v3/files/"+fileId, "GET", "{}", new ParameterizedTypeReference<Response<QueryFileStatus>>(){ });
        return responseEntity.getBody();
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record QueryFileStatus(String fileId,String fileName, String fileSize, int fileStatus, String fileDownloadUrl, int fileTotalPageCount, float pageWidth, float pageHeight) { }


}
