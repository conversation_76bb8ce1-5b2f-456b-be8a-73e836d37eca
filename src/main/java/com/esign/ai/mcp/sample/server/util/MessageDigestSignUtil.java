package com.esign.ai.mcp.sample.server.util;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.Security;
import java.util.Base64;

/**
 * 消息摘要&签名工具
 */
public class MessageDigestSignUtil {

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    /**
     * 先进行MD5摘要再进行Base64编码获取摘要字符串
     *
     * @param str
     * @return
     */
    public static String md5AndBase64(String str) {
        if (str == null) {
            throw new IllegalArgumentException("inStr can not be null");
        }
        return md5AndBase64(toBytes(str));
    }

    /**
     * 先进行MD5摘要再进行Base64编码获取摘要字符串
     *
     * @return
     */
    public static String md5AndBase64(byte[] bytes) {
        if (bytes == null) {
            throw new IllegalArgumentException("bytes can not be null");
        }
        try {
            final MessageDigest md = MessageDigest.getInstance("MD5");
            md.reset();
            md.update(bytes);
            return base64String(md.digest());
        } catch (final NoSuchAlgorithmException e) {
            throw new IllegalArgumentException("unknown algorithm MD5");
        }
    }

    /**
     * 根据指定哈希算法，对指定数据先哈希，再 base64 编码。
     *
     * @param algorithm 哈希算法。
     * @param str 原始字符串数据。
     * @return 结果。
     */
    public static String hashAndBase64(String algorithm, String str) {
        if (str == null) {
            throw new IllegalArgumentException("str can not be null");
        }
        return hashAndBase64(algorithm, toBytes(str));
    }

    /**
     * 根据指定哈希算法，对指定数据先哈希，再 base64 编码。
     *
     * @param algorithm 哈希算法。
     * @param bytes 原始字节数据。
     * @return 结果。
     */
    public static String hashAndBase64(String algorithm, byte[] bytes) {
        if (bytes == null) {
            throw new IllegalArgumentException("bytes can not be null");
        }

        try {
            MessageDigest md = MessageDigest.getInstance(algorithm);
            md.reset();
            md.update(bytes);
            return base64String(md.digest());
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalArgumentException(String.format("Unknown algorithm %s", algorithm));
        }
    }

    /**
     * 签名
     * @return
     */
    public static String sign(String algorithm, String secret, String message) throws Exception {
        Mac mac = Mac.getInstance(algorithm);
        byte[] keyBytes = secret.getBytes("UTF-8");
        mac.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, algorithm));

        return base64String(mac.doFinal(message.getBytes("UTF-8")));
    }

    /**
     * String转换为字节数组
     *
     * @param str
     * @return
     */
    private static byte[] toBytes(final String str) {
        if (str == null) {
            return null;
        }
        try {
            return str.getBytes("UTF-8");
        } catch (final UnsupportedEncodingException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    private static String base64String(byte[] bytes) {
        return Base64.getEncoder().encodeToString(bytes);
    }

}
