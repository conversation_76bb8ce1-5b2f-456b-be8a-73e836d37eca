package com.esign.ai.mcp.sample.server.configuration;

import com.esign.ai.mcp.sample.server.tools.IToolService;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * @ClassName ToolCallConfiguration
 * @Description
 * <AUTHOR>
 * @Date 2025/4/10 15:30
 **/
@Configuration
public class ToolCallConfiguration {

    @Bean
    public ToolCallbackProvider toolCall(ApplicationContext context) {
        Map<String, IToolService> beans = context.getBeansOfType(IToolService.class);
        return MethodToolCallbackProvider.builder().toolObjects(beans.values().toArray()).build();
    }

    //测试
    @Bean
    public ToolCallback toUpperCase() {
        return FunctionToolCallback.builder("toUpperCase", (TextInput input) -> input.input().toUpperCase())
                .inputType(TextInput.class)
                .description("Put the text to upper case")
                .build();
    }

    public record TextInput(String input) {
    }
}
