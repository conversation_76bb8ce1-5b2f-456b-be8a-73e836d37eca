package com.esign.ai.mcp.sample.server.tools;

import com.alibaba.fastjson.JSONObject;
import com.esign.ai.mcp.sample.server.client.GatewayClient;
import com.esign.ai.mcp.sample.server.tools.model.Response;
import com.esign.ai.mcp.sample.server.tools.model.output.SignFLowIdOutput;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName SignToolService
 * @Description
 * <AUTHOR>
 * @Date 2025/4/10 20:42
 **/
@Service
public class TemplateSignToolService implements IToolService{

    private static final Logger log = LoggerFactory.getLogger(TemplateSignToolService.class);

    @Resource
    GatewayClient gatewayClient;

    @Tool(name = "查询流程模板列表", description = "查询企业用户下的全部流程模板列表，可根据状态筛选")
    public Response<QuerySignTemplatesOutput> querySignTemplates(@ToolParam(description = "机构账号ID") String orgId,
                                         @ToolParam(description = "每页显示的数量，最大值：20（默认值 20）", required = false)Integer pageNum,
                                         @ToolParam(description = "查询页码（默认值 1）", required = false)Integer pageSize,
                                         @ToolParam(description = "流程模板可用状态，（默认值 null）", required = false) Integer status) {
        String url = "/v3/sign-templates?orgId="+ orgId;
        if(pageNum != null){
            url += "&pageNum="+pageNum;
        }
        if(pageSize != null){
            url += "&pageSize="+pageSize;
        }
        if(status != null){
            url += "&status="+status;
        }
        ResponseEntity<Response<QuerySignTemplatesOutput>> responseEntity = gatewayClient.doRequest(url, "GET", "", new ParameterizedTypeReference<Response<QuerySignTemplatesOutput>>() {
        });
        return responseEntity.getBody();
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record QuerySignTemplatesOutput(int total, List<SignTemplate> signTemplates) {
        @JsonIgnoreProperties(ignoreUnknown = true)
        public record SignTemplate(String signTemplateName,
                                   String signTemplateId,
                                   String createTime,
                                   String updateTime,
                                   int status,
                                   int signTemplateSource) {}
    }

    @Tool(name = "通过流程模板创建合同拟定和签署流程", description = "使用signTemplateId（流程模板ID）发起合同的拟定（用户填写）和签署流程")
    public Response<SignFLowIdOutput> createBySignTemplate(@ToolParam(description = "机构账号ID") String orgId,
                                                           @ToolParam(description = "流程模板ID") String signTemplateId,
                                                           @ToolParam(description = "签署任务主题") String signFlowTitle,
                                                           @ToolParam(description = "个人参与方信息", required = false) PsnParticipant psnParticipant,
                                                           @ToolParam(description = "企业参与方信息", required = false) OrgParticipant orgParticipant
                                                           ) {

        ResponseEntity<Response<QueryTemplateDetail>> entity = gatewayClient.doRequest("/v3/sign-templates/detail?orgId="+orgId+"&signTemplateId=" + signTemplateId, "GET", "", new ParameterizedTypeReference<Response<QueryTemplateDetail>>() {});
        var templateResp = entity.getBody();
        //组装参与方信息
        List<CreateSignFlowInput.Participant> participants = new ArrayList<>();
        if(templateResp == null || templateResp.getData() ==null){
            throw new RuntimeException("模版不存在");
        }
        templateResp.getData().participants().forEach(participant -> {
            if(participant.participantType() == 1){//企业
                if(orgParticipant!= null){
                    participants.add(new CreateSignFlowInput.Participant(participant.participantId, orgParticipant, null));
                }
            }else if(participant.participantType() == 2){//个人
                if(psnParticipant != null){
                    participants.add(new CreateSignFlowInput.Participant(participant.participantId, null, psnParticipant));
                }
            }
        });

        //发起方信息
//        var transactor = new CreateSignFlowInput.SignFlowInitiator.Transactor("");
//        var signFlowInitiator = new CreateSignFlowInput.SignFlowInitiator("", transactor);
        //签署流程配置项
        var signConfig = new CreateSignFlowInput.SignFlowConfig.SignConfig( "1,2", false, true);
        var noticeConfig = new CreateSignFlowInput.SignFlowConfig.NoticeConfig("1");
        var signFlowConfig = new CreateSignFlowInput.SignFlowConfig(signFlowTitle, true, true, null, signConfig, noticeConfig);

        //模版发起签署流程请求体
        CreateSignFlowInput createSignFlowInput = new CreateSignFlowInput(signTemplateId, null, signFlowConfig, participants);
        String body = JSONObject.toJSONString(createSignFlowInput);
        ResponseEntity<Response<SignFLowIdOutput>> responseEntity = gatewayClient.doRequest("/v3/sign-flow/create-by-sign-template", "POST", body, new ParameterizedTypeReference<Response<SignFLowIdOutput>>() {});
       return responseEntity.getBody();
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record QueryTemplateDetail(String orgId, String signTemplateId, String signTemplateName, int signTemplateStatus, List<Participant> participants) {
        @JsonIgnoreProperties
        public record Participant(String participantId, String participantFlag, int participantType, String participateBizType, int participantSetMode, OrgParticipant orgParticipant, PsnParticipant psnParticipant) {}
    }
    @JsonIgnoreProperties
    public record OrgParticipant(@ToolParam(description = "企业参与方企业ID", required = false)String orgId, @ToolParam(description = "企业参与方企业名称", required = false)String orgName, Transactor transactor){
        @JsonIgnoreProperties
        public record Transactor(@ToolParam(description = "企业参与方经办人个人ID", required = false)String transactorPsnId,
                                 @ToolParam(description = "企业参与方经办人手机号/邮箱", required = false)String transactorPsnAccount,
                                 @ToolParam(description = "企业参与方经办人姓名", required = false)String transactorName){}
    }
    @JsonIgnoreProperties
    public record PsnParticipant(@ToolParam(description = "个人参与方账号id", required = false)String psnId,
                                 @ToolParam(description = "个人参与方手机号/邮箱", required = false)String psnAccount,
                                 @ToolParam(description = "个人参与方姓名", required = false)String psnName){

    }


    /**
     * 模版发起签署流程请求体
     * @param signTemplateId
     * @param signFlowInitiator
     * @param signFlowConfig
     * @param participants
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public record CreateSignFlowInput(String signTemplateId, SignFlowInitiator signFlowInitiator, SignFlowConfig signFlowConfig, List<Participant> participants) {
        @JsonIgnoreProperties(ignoreUnknown = true)
        public record SignFlowInitiator(String orgId, Transactor transactor) {
            @JsonIgnoreProperties(ignoreUnknown = true)
            public record Transactor(String psnId) {}
        }
        @JsonIgnoreProperties(ignoreUnknown = true)
        public record SignFlowConfig(String signFlowTitle, boolean autoStart, boolean autoFinish, String notifyUrl, SignConfig signConfig, NoticeConfig noticeConfig) {
            @JsonIgnoreProperties(ignoreUnknown = true)
            public record SignConfig(String availableSignClientTypes, boolean autoFillAndSubmit, boolean editComponentValue){}
            @JsonIgnoreProperties(ignoreUnknown = true)
            public record NoticeConfig(String noticeTypes){}
        }
        @JsonIgnoreProperties(ignoreUnknown = true)
        public record Participant(String participantId, OrgParticipant orgParticipant, PsnParticipant psnParticipant){

        }
    }
}
