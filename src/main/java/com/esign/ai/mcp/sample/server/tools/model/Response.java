package com.esign.ai.mcp.sample.server.tools.model;

import lombok.Data;

/**
 * @ClassName Response
 * @Description
 * <AUTHOR>
 * @Date 2025/4/14 11:10
 **/
@Data
public class Response<T>{
    private Integer code;
    private String message;
    private T data;

    public Response(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }
}
