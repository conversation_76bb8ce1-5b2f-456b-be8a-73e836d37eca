package com.esign.ai.mcp.sample.server.client;

import com.alibaba.fastjson.JSONObject;
import com.esign.ai.mcp.sample.server.util.MessageDigestSignUtil;
import io.micrometer.common.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * @ClassName GatewayClient
 * @Description
 * <AUTHOR>
 * @Date 2025/4/10 09:49
 **/
@Service
public class GatewayClient {

    private static final Logger log = LoggerFactory.getLogger(GatewayClient.class);

    private final static String OPEN_GATEWAY_DIGEST_MD5 = "MD5";
    private final static String OPEN_GATEWAY_ALGORITHM_SHA256 = "HmacSHA256";
    private static final Map<String, String> URL_MAP = Map.of(
            "test","https://trestapi.tsign.cn",
            "sml","https://smlopenapi.esign.cn",
            "prod","https://openapi.esign.cn"
    );

    private final String app_id;
    private final String app_secret;
    private final String env;

    private final RestClient restClient;

    public GatewayClient() {
        app_id = System.getenv("APP_ID");
        app_secret = System.getenv("APP_SECRET");
        env = System.getenv("ENV");
//        app_id = "4438758872";
//        app_secret = "1c1d6b441af05090d1c3407f042cf53e";
//        env = "sml";
        String url = URL_MAP.get(env);
        log.info("app_id={}, app_secret={}, env={}, url={}", app_id, app_secret, env, url);
        this.restClient = RestClient.builder()
                .baseUrl(url)
                .defaultHeader("Accept", "*/*")
                .defaultHeader("Content-Type", "application/json; charset=UTF-8")
                .defaultHeader("X-Tsign-Open-Auth-Mode", "signature")
                .defaultHeader("X-Tsign-Open-App-Id", app_id)
                .build();
    }

    public RestClient.ResponseSpec doHttpRequest(String url, String httpMethod, String body){
        body = StringUtils.isBlank(body)?"":body;
        String md5 = getMd5(body);
        String signature = getSignature(url, httpMethod, md5);

        RestClient.ResponseSpec retrieve = restClient.method(HttpMethod.valueOf(httpMethod))
                .uri(url)
                .headers(httpHeaders -> {
                    httpHeaders.add("Content-MD5", md5);
                    httpHeaders.add("X-Tsign-Open-Ca-Signature", signature);
                    httpHeaders.add("X-Tsign-Open-Ca-Timestamp", System.currentTimeMillis() + "");
                }).body(body)
                .retrieve();

        return retrieve;
    }

    public ResponseEntity doRequest(String url, String httpMethod, String body, ParameterizedTypeReference reference){
        body = StringUtils.isBlank(body)?"":body;
        String md5 = getMd5(body);
        String signature = getSignature(url, httpMethod, md5);

        ResponseEntity responseEntity = restClient.method(HttpMethod.valueOf(httpMethod))
                .uri(url)
                .headers(httpHeaders -> {
                    httpHeaders.add("Content-MD5", md5);
                    httpHeaders.add("X-Tsign-Open-Ca-Signature", signature);
                    httpHeaders.add("X-Tsign-Open-Ca-Timestamp", System.currentTimeMillis() + "");
                }).body(body)
                .retrieve().toEntity(reference);
//        if(!responseEntity.getStatusCode().isSameCodeAs(HttpStatus.OK)){
            log.info("api请求结果：\nurl={} \nhttpMethod={} \nbody={} \nresponse={}", url, httpMethod, body, JSONObject.toJSONString(responseEntity));
//        }
        return responseEntity;
    }

    private String getMd5(String body){
        return MessageDigestSignUtil.hashAndBase64(OPEN_GATEWAY_DIGEST_MD5, body);
    }

    private String getSignature(String url, String httpMethod, String md5){
        //对url的params.key排序
        String path = this.getUrlWithSortPath(url);
        //构建待签名字符串
        String signStr = this.getSignStr(httpMethod, "*/*", md5, "application/json; charset=UTF-8", "", "", path);
        //sign
        try {
            return MessageDigestSignUtil.sign(OPEN_GATEWAY_ALGORITHM_SHA256, app_secret, signStr);
        } catch (Exception e) {
            log.error("计算签名失败，signStr={}", signStr, e);
            throw new RuntimeException(e);
        }
    }

    private String getUrlWithSortPath(String url){
        String[] str = url.split("\\?");
        if(str.length==1){
            return url;
        }
        Map<String, String> paramsMap = rebuildFormBody(str[1]);

        SortedMap<String, Object> sortedMap = new TreeMap<>(paramsMap);
        String path = str[0] + "?" + sortedMap.entrySet().stream()
                .map(e-> e.getKey() + "="+(e.getValue()))
                .collect(Collectors.joining("&"));
        return path;
    }

    private Map<String, String> rebuildFormBody(String formBodyText) {
        Map<String, String> bodies = new HashMap<>();
        if (StringUtils.isBlank(formBodyText)) {
            return bodies;
        }

        try {
            Arrays.stream(formBodyText.trim().split("&")).forEach(part -> {
                String[] param = part.split("=");
                String value = param[1];
                try {
                    value = URLDecoder.decode(param[1], "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    log.warn("URL decode failed.{0}", e);
                }
                bodies.put(param[0], value);
            });
            return bodies;
        } catch (Exception e) {
            log.warn("url 参数格式错误, str={}", formBodyText, e);
            throw new RuntimeException("url 参数格式错误");
        }
    }


    private String getSignStr(String httpMethod, String accept, String contentMd5,
                                    String contentType, String date, String headers, String url){
        String newLine = "".equals(headers)?"":"\n";

        return httpMethod + "\n" +
                accept + "\n" +
                contentMd5 + "\n" +
                contentType + "\n" +
                date + "\n" +
                headers + newLine +
                url;
    }
}
