# spring.main.web-application-type=none

# NOTE: You must disable the banner and the console logging 
# to allow the STDIO transport to work !!!
spring.main.banner-mode=off
# logging.pattern.console=

spring.ai.mcp.server.name=my-mcp-server
spring.ai.mcp.server.version=0.0.1

logging.file.name=./log/starter-webflux-server.log
#server.port=8080

spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB